"""
Federated Learning Client for D³AFD
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, List, Optional, Tuple
import copy
import logging

from ..models.base_models import TeacherModel, PersonalizedModel, PersonalizedHead
from ..models.domain_discriminator import DomainDiscriminator
from ..data.amazon_dataset import AmazonReviewDataset
from ..utils.memory_manager import get_memory_manager, memory_cleanup_decorator

logger = logging.getLogger(__name__)

class FederatedClient:
    """Federated learning client for D³AFD"""

    def __init__(self, client_id: str = None, domain: str = None, config=None,
                 local_data=None, train_dataloader: DataLoader = None,
                 val_dataloader: DataLoader = None, test_dataloader: DataLoader = None):
        # Support both old and new initialization styles
        if isinstance(client_id, int):
            # Old style initialization
            self.client_id = client_id
            self.config = config
            self.device = torch.device(config.experiment.device)
            self.train_dataloader = train_dataloader
            self.val_dataloader = val_dataloader
            self.test_dataloader = test_dataloader
            self.client_domains = self._extract_client_domains()
            self.domain = self.client_domains[0] if self.client_domains else "default"
            self.local_data = None
        else:
            # New style initialization for multi-algorithm support
            self.client_id = client_id
            self.domain = domain
            self.config = config
            self.device = torch.device(config.experiment.device)
            self.local_data = local_data

            # Initialize tokenizer first (needed for data loaders)
            if hasattr(config, 'model') and hasattr(config.model, 'teacher_model'):
                from transformers import AutoTokenizer
                self.tokenizer = AutoTokenizer.from_pretrained(config.model.teacher_model)
            else:
                self.tokenizer = None

            # Initialize data loaders
            if local_data is not None:
                # Create data loaders from local_data if provided
                if len(local_data) > 0 and hasattr(config, 'data'):
                    logger.info(f"Client {self.client_id}: Creating dataloader from {len(local_data)} samples")
                    self.train_dataloader = self._create_dataloader(local_data, 'train')
                    self.val_dataloader = None  # Will be created if needed
                    self.test_dataloader = None  # Will be created if needed
                else:
                    logger.warning(f"Client {self.client_id}: No local data provided or empty data (len: {len(local_data) if local_data else 0})")
                    self.train_dataloader = train_dataloader
                    self.val_dataloader = val_dataloader
                    self.test_dataloader = test_dataloader
            else:
                # Use provided dataloaders
                self.train_dataloader = train_dataloader
                self.val_dataloader = val_dataloader
                self.test_dataloader = test_dataloader

            self.client_domains = [domain] if domain else []

        # Models
        from ..models.base_models import StudentModel
        self.model = StudentModel(config).to(self.device)
        self.teacher_model = self.model  # For compatibility
        self.domain_discriminator = None
        self.personalized_model = None

        # Training history
        self.training_history = []

        logger.info(f"Client {self.client_id} initialized with domain: {self.domain}")

    def _create_dataloader(self, data: List[Dict], split_type: str = 'train') -> DataLoader:
        """Create DataLoader from data list"""
        if not data:
            logger.warning(f"Client {self.client_id}: Empty data for {split_type}")
            return None
            
        # Extract text, labels, and domains
        texts = [item['text'] for item in data]
        labels = [item['label'] for item in data]
        domains = [item['domain'] for item in data]
        
        # Create dataset
        from ..data.amazon_dataset import AmazonReviewDataset
        dataset = AmazonReviewDataset(
            texts=texts,
            labels=labels,
            domains=domains,
            tokenizer=self.tokenizer,
            max_length=getattr(self.config.data, 'max_length', 512)
        )
        
        # Create DataLoader
        batch_size = self.config.training.local_batch_size if split_type == 'train' else 32
        
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=(split_type == 'train'),
            num_workers=2
        )
    
    def _extract_client_domains(self) -> List[str]:
        """Extract unique domains from client's training data"""
        domains = set()
        for batch in self.train_dataloader:
            domains.update(batch['domain'])
        return list(domains)
    
    def initialize_models(self):
        """Initialize local models"""
        # Initialize teacher model
        self.teacher_model = TeacherModel(self.config).to(self.device)
        
        # Initialize domain discriminator for primary domain
        primary_domain = self.client_domains[0] if self.client_domains else "default"
        self.domain_discriminator = DomainDiscriminator(
            self.config, primary_domain
        ).to(self.device)
        
        logger.info(f"Client {self.client_id}: Models initialized")

    def local_train(self) -> Dict[str, float]:
        """
        Train local model on its own data.
        The model should have been updated with the global model state before calling this.
        """
        # The model is assumed to be initialized and updated by the server.
        if self.model is None:
            logger.error(f"Client {self.client_id}: Model not initialized before local_train")
            # Fallback to initialize a new model, though this indicates a flow error.
            from ..models.base_models import StudentModel
            self.model = StudentModel(self.config).to(self.device)

        # In our unified framework, the client's main model is the teacher model.
        return self.train_local_teacher()

    def _train_local_model(self, num_epochs: Optional[int] = None) -> Dict[str, float]:
        """Train local model with basic setup"""
        if num_epochs is None:
            num_epochs = getattr(self.config.training, 'local_epochs', 3)

        if self.model is None or self.train_dataloader is None:
            logger.warning(f"Client {self.client_id}: No model or data for training")
            return {'loss': 0.0, 'accuracy': 0.0}

        logger.info(f"Client {self.client_id}: Training local model for {num_epochs} epochs")

        self.model.train()
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=getattr(self.config.training, 'learning_rate', 2e-5)
        )

        total_loss = 0
        num_batches = 0

        for epoch in range(num_epochs):
            epoch_loss = 0
            epoch_batches = 0

            for batch in self.train_dataloader:
                try:
                    # Move to device
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)

                    # Forward pass
                    outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                    logits = outputs.logits if hasattr(outputs, 'logits') else outputs

                    # Compute loss
                    loss = torch.nn.functional.cross_entropy(logits, labels)

                    # Backward pass
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()

                    epoch_loss += loss.item()
                    epoch_batches += 1

                except Exception as e:
                    logger.error(f"Error in training batch: {e}")
                    continue

            if epoch_batches > 0:
                avg_epoch_loss = epoch_loss / epoch_batches
                total_loss += avg_epoch_loss
                num_batches += 1
                logger.info(f"Client {self.client_id} Epoch {epoch+1}/{num_epochs}, Loss: {avg_epoch_loss:.4f}")

        avg_loss = total_loss / max(num_batches, 1)
        return {'loss': avg_loss, 'accuracy': 0.0}  # TODO: Add accuracy calculation

    def train_local_teacher(self, num_epochs: Optional[int] = None) -> Dict[str, float]:
        """Train local teacher model"""
        if num_epochs is None:
            num_epochs = int(self.config.training.local_epochs)
        
        logger.info(f"Client {self.client_id}: Training local teacher for {num_epochs} epochs")
        
        self.teacher_model.train()

        # Choose optimizer based on configuration
        optimizer_type = getattr(self.config.training, 'optimizer_type', 'adamw')

        if optimizer_type.lower() == 'sgd':
            # SGD optimizer for speed and memory efficiency
            optimizer = torch.optim.SGD(
                self.teacher_model.parameters(),
                lr=float(self.config.training.local_lr),
                momentum=getattr(self.config.training, 'momentum', 0.9),
                weight_decay=getattr(self.config.training, 'weight_decay', 1e-4),
                nesterov=getattr(self.config.training, 'nesterov', True)
            )
        else:
            # AdamW optimizer (default)
            try:
                param_groups = [
                    {'params': self.teacher_model.bert.embeddings.parameters(), 'lr': float(self.config.training.local_lr) * 0.1},
                    {'params': self.teacher_model.bert.encoder.parameters(), 'lr': float(self.config.training.local_lr) * 0.5},
                    {'params': self.teacher_model.classifier.parameters(), 'lr': float(self.config.training.local_lr)}
                ]
            except AttributeError:
                # Fallback if model structure is different
                param_groups = [{'params': self.teacher_model.parameters(), 'lr': float(self.config.training.local_lr)}]

            optimizer = torch.optim.AdamW(
                param_groups,
                weight_decay=getattr(self.config.training, 'weight_decay', 0.01)
            )

        # Add learning rate scheduler
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=int(num_epochs), eta_min=float(self.config.training.local_lr) * 0.01
        )

        # Use label smoothing to prevent overfitting
        label_smoothing = getattr(self.config.training, 'label_smoothing', 0.1)
        criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)

        # Early stopping parameters
        best_val_loss = float('inf')
        epochs_no_improve = 0
        patience = 3 # Stop after 3 epochs of no improvement in validation loss

        total_loss = 0
        total_correct = 0
        total_samples = 0

        for epoch in range(num_epochs):
            self.teacher_model.train()
            epoch_loss = 0
            epoch_correct = 0
            epoch_samples = 0
            
            for batch in self.train_dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                outputs = self.teacher_model(input_ids=input_ids, attention_mask=attention_mask)
                loss = criterion(outputs.logits, labels)
                
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.teacher_model.parameters(), self.config.training.gradient_clip_norm)
                optimizer.step()
                
                epoch_loss += loss.item()
                predictions = torch.argmax(outputs.logits, dim=1)
                epoch_correct += (predictions == labels).sum().item()
                epoch_samples += labels.size(0)
            
            scheduler.step()
            
            epoch_accuracy = epoch_correct / epoch_samples
            avg_loss = epoch_loss / len(self.train_dataloader)
            logger.info(f"Client {self.client_id} - Train Epoch {epoch+1}/{num_epochs}: Loss: {avg_loss:.4f}, Accuracy: {epoch_accuracy:.4f}")

            # Validation step for early stopping
            if self.val_dataloader:
                val_metrics = self.evaluate_model(self.teacher_model, self.val_dataloader)
                val_loss = val_metrics['loss']
                logger.info(f"Client {self.client_id} - Validation Epoch {epoch+1}/{num_epochs}: Val Loss: {val_loss:.4f}, Val Accuracy: {val_metrics['accuracy']:.4f}")

                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    epochs_no_improve = 0
                    # Optionally save the best model state
                    best_model_state = copy.deepcopy(self.teacher_model.state_dict())
                else:
                    epochs_no_improve += 1
                
                if epochs_no_improve >= patience:
                    logger.info(f"Client {self.client_id}: Early stopping triggered after {epoch+1} epochs.")
                    self.teacher_model.load_state_dict(best_model_state) # Restore best model
                    break
            
            total_loss += epoch_loss
            total_correct += epoch_correct
            total_samples += epoch_samples
            
            memory_manager = get_memory_manager()
            memory_manager.clear_cache()
        
        # Final statistics
        final_accuracy = total_correct / total_samples
        final_loss = total_loss / (len(self.train_dataloader) * int(num_epochs))
        
        training_stats = {
            'loss': final_loss,
            'accuracy': final_accuracy,
            'epochs': num_epochs
        }
        
        self.training_history.append(training_stats)
        
        logger.info(f"Client {self.client_id}: Local teacher training completed. "
                   f"Final accuracy: {final_accuracy:.4f}")
        
        return training_stats
    
    def train_domain_discriminator(self, negative_samples: List[str]) -> Dict[str, float]:
        """Train domain discriminator"""
        logger.info(f"Client {self.client_id}: Training domain discriminator")
        
        # Collect positive samples from local data
        positive_samples = []
        for batch in self.train_dataloader:
            positive_samples.extend(batch['text'])
        
        # Limit samples for efficiency
        positive_samples = positive_samples[:500]
        negative_samples = negative_samples[:500]
        
        # Train discriminator
        self.domain_discriminator.train()
        optimizer = torch.optim.AdamW(
            self.domain_discriminator.parameters(),
            lr=self.config.training.local_lr
        )
        criterion = nn.BCELoss()
        
        # Prepare training data
        all_texts = positive_samples + negative_samples
        all_labels = [1.0] * len(positive_samples) + [0.0] * len(negative_samples)
        
        # Training loop
        num_epochs = 3
        batch_size = self.config.training.local_batch_size
        
        total_loss = 0
        total_correct = 0
        total_samples = 0
        
        for epoch in range(num_epochs):
            # Shuffle data
            indices = torch.randperm(len(all_texts))
            
            for i in range(0, len(all_texts), batch_size):
                batch_indices = indices[i:i+batch_size]
                batch_texts = [all_texts[idx] for idx in batch_indices]
                batch_labels = torch.tensor([all_labels[idx] for idx in batch_indices],
                                          dtype=torch.float).to(self.device)
                
                # Tokenize
                from transformers import AutoTokenizer
                tokenizer = AutoTokenizer.from_pretrained(self.config.model.teacher_model)
                
                encoding = tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=getattr(self.config.data, 'max_length', 512),
                    return_tensors='pt'
                ).to(self.device)
                
                # Forward pass
                outputs = self.domain_discriminator(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )
                
                loss = criterion(outputs, batch_labels)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Statistics
                total_loss += loss.item()
                predictions = (outputs > 0.5).float()
                total_correct += (predictions == batch_labels).sum().item()
                total_samples += len(batch_labels)
        
        accuracy = total_correct / total_samples
        avg_loss = total_loss / (total_samples // batch_size)
        
        logger.info(f"Client {self.client_id}: Domain discriminator training completed. "
                   f"Accuracy: {accuracy:.4f}")
        
        return {'loss': avg_loss, 'accuracy': accuracy}
    
    def get_teacher_predictions(self, pseudo_samples: List[Dict]) -> torch.Tensor:
        """Get teacher model predictions for pseudo samples"""
        self.teacher_model.eval()
        
        predictions = []
        
        with torch.no_grad():
            # Process in batches
            batch_size = 32
            for i in range(0, len(pseudo_samples), batch_size):
                batch_samples = pseudo_samples[i:i+batch_size]
                batch_texts = [sample['text'] for sample in batch_samples]
                
                # Tokenize
                from transformers import AutoTokenizer
                tokenizer = AutoTokenizer.from_pretrained(self.config.model.teacher_model)
                
                encoding = tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=getattr(self.config.data, 'max_length', 512),
                    return_tensors='pt'
                ).to(self.device)
                
                # Get predictions
                outputs = self.teacher_model(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )
                
                predictions.append(outputs.logits)
        
        return torch.cat(predictions, dim=0)
    
    def get_domain_weights(self, texts: List[str]) -> torch.Tensor:
        """Get domain relevance weights for texts"""
        self.domain_discriminator.eval()
        
        weights = []
        
        with torch.no_grad():
            # Process in batches
            batch_size = 32
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]
                
                # Tokenize
                from transformers import AutoTokenizer
                tokenizer = AutoTokenizer.from_pretrained(self.config.model.teacher_model)
                
                encoding = tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=getattr(self.config.data, 'max_length', 512),
                    return_tensors='pt'
                ).to(self.device)
                
                # Get weights
                domain_weights = self.domain_discriminator(
                    input_ids=encoding['input_ids'],
                    attention_mask=encoding['attention_mask']
                )
                
                weights.append(domain_weights)
        
        return torch.cat(weights, dim=0)

    def create_personalized_model(self, global_backbone: nn.Module) -> PersonalizedModel:
        """Create personalized model with global backbone and local head"""
        logger.info(f"Client {self.client_id}: Creating personalized model")

        # Get backbone feature size
        if hasattr(global_backbone, 'encoder'):
            # For our custom models
            feature_size = global_backbone.encoder.config.hidden_size
        else:
            # For transformer models
            feature_size = global_backbone.config.hidden_size

        # Create personalized head
        personalized_head = PersonalizedHead(self.config, feature_size).to(self.device)

        # Create personalized model
        self.personalized_model = PersonalizedModel(
            global_backbone, personalized_head
        ).to(self.device)

        return self.personalized_model

    def fine_tune_personalized_model(self, num_epochs: Optional[int] = None) -> Dict[str, float]:
        """Fine-tune personalized model on local data"""
        if num_epochs is None:
            num_epochs = self.config.training.personalization_epochs

        if self.personalized_model is None:
            raise ValueError("Personalized model not created. Call create_personalized_model first.")

        logger.info(f"Client {self.client_id}: Fine-tuning personalized model for {num_epochs} epochs")

        self.personalized_model.train()

        # Only optimize personalized head parameters
        optimizer = torch.optim.AdamW(
            self.personalized_model.head.parameters(),
            lr=self.config.training.personalization_lr
        )

        criterion = nn.CrossEntropyLoss()

        total_loss = 0
        total_correct = 0
        total_samples = 0

        for epoch in range(num_epochs):
            epoch_loss = 0
            epoch_correct = 0
            epoch_samples = 0

            for batch in self.train_dataloader:
                # Move batch to device
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                # Forward pass
                outputs = self.personalized_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )

                # Supervised loss
                sup_loss = criterion(outputs.logits, labels)

                # Optional: Add distillation loss with global model
                total_loss_batch = sup_loss

                if hasattr(self.config.training, 'mu') and self.config.training.mu > 0:
                    # Get global model predictions (teacher)
                    with torch.no_grad():
                        global_outputs = self.personalized_model.backbone(
                            input_ids=input_ids,
                            attention_mask=attention_mask
                        )

                    # KL divergence loss
                    kl_loss = F.kl_div(
                        F.log_softmax(outputs.logits / self.config.training.temperature, dim=1),
                        F.softmax(global_outputs.logits / self.config.training.temperature, dim=1),
                        reduction='batchmean'
                    )

                    total_loss_batch = sup_loss + self.config.training.mu * kl_loss

                # Backward pass
                optimizer.zero_grad()
                total_loss_batch.backward()
                optimizer.step()

                # Statistics
                epoch_loss += total_loss_batch.item()
                predictions = torch.argmax(outputs.logits, dim=1)
                epoch_correct += (predictions == labels).sum().item()
                epoch_samples += labels.size(0)

            # Log epoch results
            epoch_accuracy = epoch_correct / epoch_samples
            avg_loss = epoch_loss / len(self.train_dataloader)

            logger.info(f"Client {self.client_id} - Personalization Epoch {epoch+1}/{num_epochs}: "
                       f"Loss: {avg_loss:.4f}, Accuracy: {epoch_accuracy:.4f}")

            total_loss += epoch_loss
            total_correct += epoch_correct
            total_samples += epoch_samples

        # Final statistics
        final_accuracy = total_correct / total_samples
        final_loss = total_loss / (len(self.train_dataloader) * num_epochs)

        personalization_stats = {
            'loss': final_loss,
            'accuracy': final_accuracy,
            'epochs': num_epochs
        }

        logger.info(f"Client {self.client_id}: Personalized model fine-tuning completed. "
                   f"Final accuracy: {final_accuracy:.4f}")

        return personalization_stats

    def evaluate_model(self, model: nn.Module, dataloader: DataLoader) -> Dict[str, float]:
        """Evaluate model performance"""
        model.eval()

        total_loss = 0
        total_correct = 0
        total_samples = 0
        all_predictions = []
        all_labels = []

        criterion = nn.CrossEntropyLoss()

        with torch.no_grad():
            for batch in dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )

                loss = criterion(outputs.logits, labels)
                total_loss += loss.item()

                predictions = torch.argmax(outputs.logits, dim=1)
                total_correct += (predictions == labels).sum().item()
                total_samples += labels.size(0)

                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        accuracy = total_correct / total_samples
        avg_loss = total_loss / len(dataloader)

        # Calculate F1 scores
        from sklearn.metrics import f1_score
        f1_macro = f1_score(all_labels, all_predictions, average='macro')
        f1_weighted = f1_score(all_labels, all_predictions, average='weighted')

        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted
        }

    def get_model_state_dict(self, model_type: str = 'teacher') -> Dict:
        """Get model state dictionary"""
        if model_type == 'teacher':
            return self.teacher_model.state_dict()
        elif model_type == 'discriminator':
            return self.domain_discriminator.state_dict()
        elif model_type == 'personalized':
            return self.personalized_model.state_dict()
        else:
            raise ValueError(f"Unknown model type: {model_type}")

    def load_model_state_dict(self, state_dict: Dict, model_type: str = 'teacher'):
        """Load model state dictionary"""
        if model_type == 'teacher' or model_type == 'main':
            self.model.load_state_dict(state_dict)
            self.teacher_model.load_state_dict(state_dict)
        elif model_type == 'discriminator':
            self.domain_discriminator.load_state_dict(state_dict)
        elif model_type == 'personalized':
            self.personalized_model.load_state_dict(state_dict)
        else:
            raise ValueError(f"Unknown model type: {model_type}")

    def update_model(self, global_model_state_dict: Dict):
        """Update the client's local model with the global model state"""
        logger.info(f"Client {self.client_id}: Updating local model with global state.")
        self.load_model_state_dict(global_model_state_dict, model_type='main')

    def get_teacher_model_state_dict(self) -> Dict:
        """Get teacher model state dictionary for federated averaging"""
        if self.teacher_model is None:
            raise ValueError("Teacher model not initialized")
        return self.teacher_model.state_dict()

    def get_teacher_model(self):
        """Returns the trained teacher model."""
        return self.teacher_model

    @property
    def train_data(self):
        """Get training data for sample count calculation"""
        # Return a list-like object that supports len()
        return self.train_dataloader.dataset
