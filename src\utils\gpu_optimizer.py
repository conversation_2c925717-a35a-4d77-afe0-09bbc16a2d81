"""
GPU Optimization Utilities for D³AFD Framework
Provides advanced GPU memory management and acceleration techniques
"""

import torch
import gc
import os
import logging
import time
from typing import Optional, Dict, Any, Callable
import psutil

logger = logging.getLogger(__name__)

class GPUOptimizer:
    """Advanced GPU optimization and memory management"""
    
    def __init__(self, aggressive_mode: bool = True):
        self.aggressive_mode = aggressive_mode
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.gpu_info = self._get_gpu_info()
        self.memory_history = []
        
    def _get_gpu_info(self) -> Dict[str, Any]:
        """Get detailed GPU information"""
        if not torch.cuda.is_available():
            return {"available": False}
        
        gpu_props = torch.cuda.get_device_properties(0)
        return {
            "available": True,
            "name": gpu_props.name,
            "total_memory": gpu_props.total_memory / 1024**3,  # GB
            "major": gpu_props.major,
            "minor": gpu_props.minor,
            "multi_processor_count": gpu_props.multi_processor_count
        }
    
    def setup_environment(self):
        """Setup optimized GPU environment"""
        if not self.gpu_info["available"]:
            logger.warning("CUDA not available, skipping GPU optimizations")
            return
        
        logger.info("🚀 Setting up GPU optimization environment...")
        
        # Enable CUDA optimizations
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # Set environment variables
        env_vars = {
            'PYTORCH_CUDA_ALLOC_CONF': 'expandable_segments:True,max_split_size_mb:1024',
            'TOKENIZERS_PARALLELISM': 'true',
            'OMP_NUM_THREADS': '12',
            'CUDA_LAUNCH_BLOCKING': '0'
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
        
        # Enable advanced optimizations
        try:
            torch.backends.cuda.enable_flash_sdp(True)
            torch.backends.cuda.enable_mem_efficient_sdp(True)
            torch.backends.cuda.enable_math_sdp(True)
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
        except Exception as e:
            logger.warning(f"Some optimizations not available: {e}")
        
        # Set memory fraction
        memory_fraction = self._calculate_memory_fraction()
        torch.cuda.set_per_process_memory_fraction(memory_fraction)
        
        logger.info(f"✅ GPU optimization setup complete:")
        logger.info(f"   - Device: {self.gpu_info['name']}")
        logger.info(f"   - Memory: {self.gpu_info['total_memory']:.2f} GB")
        logger.info(f"   - Memory fraction: {memory_fraction*100:.0f}%")
    
    def _calculate_memory_fraction(self) -> float:
        """Calculate optimal memory fraction based on GPU memory"""
        total_memory = self.gpu_info["total_memory"]
        
        if self.aggressive_mode:
            if total_memory > 20:
                return 0.95  # Use 95% for very high-end GPUs
            elif total_memory > 16:
                return 0.92  # Use 92% for high-end GPUs
            elif total_memory > 12:
                return 0.88  # Use 88% for mid-high range GPUs
            elif total_memory > 8:
                return 0.85  # Use 85% for mid-range GPUs
            else:
                return 0.80  # Use 80% for lower-end GPUs
        else:
            # Conservative mode
            if total_memory > 16:
                return 0.85
            elif total_memory > 12:
                return 0.80
            elif total_memory > 8:
                return 0.75
            else:
                return 0.70
    
    def clear_memory(self, force: bool = False):
        """Clear GPU memory"""
        if not self.gpu_info["available"]:
            return
        
        if force:
            # Aggressive cleanup
            for _ in range(3):
                gc.collect()
                torch.cuda.empty_cache()
                torch.cuda.ipc_collect()
            time.sleep(0.1)
        else:
            gc.collect()
            torch.cuda.empty_cache()
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current GPU memory usage"""
        if not self.gpu_info["available"]:
            return {"allocated": 0, "reserved": 0, "free": 0}
        
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        total = self.gpu_info["total_memory"]
        free = total - reserved
        
        return {
            "allocated": allocated,
            "reserved": reserved,
            "free": free,
            "total": total
        }
    
    def log_memory_usage(self, stage: str = ""):
        """Log current memory usage"""
        if not self.gpu_info["available"]:
            return
        
        usage = self.get_memory_usage()
        self.memory_history.append((stage, usage))
        
        logger.info(f"GPU Memory {stage}: "
                   f"Allocated: {usage['allocated']:.2f}GB, "
                   f"Reserved: {usage['reserved']:.2f}GB, "
                   f"Free: {usage['free']:.2f}GB")
    
    def optimize_batch_size(self, base_batch_size: int, model_size_factor: float = 1.0) -> int:
        """Calculate optimal batch size based on GPU memory"""
        if not self.gpu_info["available"]:
            return base_batch_size
        
        total_memory = self.gpu_info["total_memory"]
        
        # Adjust based on model size
        adjusted_memory = total_memory / model_size_factor
        
        if adjusted_memory > 20:
            multiplier = 4.0
        elif adjusted_memory > 16:
            multiplier = 3.5
        elif adjusted_memory > 12:
            multiplier = 3.0
        elif adjusted_memory > 8:
            multiplier = 2.5
        elif adjusted_memory > 6:
            multiplier = 2.0
        else:
            multiplier = 1.5
        
        optimal_batch_size = int(base_batch_size * multiplier)
        
        # Ensure it's a reasonable size
        return min(optimal_batch_size, 128)
    
    def optimize_num_workers(self, base_workers: int = 4) -> int:
        """Calculate optimal number of data loading workers"""
        cpu_count = psutil.cpu_count(logical=True)
        
        if self.aggressive_mode:
            # Use more workers for faster data loading
            optimal_workers = min(cpu_count, 16)
        else:
            # Conservative approach
            optimal_workers = min(cpu_count // 2, 8)
        
        return max(optimal_workers, base_workers)
    
    def create_memory_efficient_dataloader(self, dataset, batch_size: int, **kwargs):
        """Create memory-efficient dataloader"""
        optimal_workers = self.optimize_num_workers(kwargs.get('num_workers', 4))
        
        dataloader_kwargs = {
            'batch_size': batch_size,
            'num_workers': optimal_workers,
            'pin_memory': True if self.gpu_info["available"] else False,
            'persistent_workers': True if optimal_workers > 0 else False,
            'prefetch_factor': 4 if optimal_workers > 0 else 2,
            **kwargs
        }
        
        return torch.utils.data.DataLoader(dataset, **dataloader_kwargs)
    
    def monitor_memory_during_training(self, func: Callable, *args, **kwargs):
        """Monitor memory usage during training function execution"""
        self.log_memory_usage("before_training")
        
        try:
            result = func(*args, **kwargs)
            self.log_memory_usage("after_training")
            return result
        except torch.cuda.OutOfMemoryError as e:
            logger.error(f"GPU OOM during training: {e}")
            self.clear_memory(force=True)
            raise e
        except Exception as e:
            logger.error(f"Error during training: {e}")
            self.clear_memory()
            raise e
    
    def get_optimization_recommendations(self) -> Dict[str, Any]:
        """Get optimization recommendations based on GPU"""
        if not self.gpu_info["available"]:
            return {"gpu_available": False}
        
        total_memory = self.gpu_info["total_memory"]
        
        recommendations = {
            "gpu_available": True,
            "gpu_name": self.gpu_info["name"],
            "total_memory": total_memory,
            "recommended_batch_sizes": {
                "local_training": self.optimize_batch_size(16),
                "distillation": self.optimize_batch_size(32),
                "evaluation": self.optimize_batch_size(64)
            },
            "recommended_workers": self.optimize_num_workers(),
            "memory_fraction": self._calculate_memory_fraction(),
            "pseudo_samples_per_domain": min(2000, int(total_memory * 100))
        }
        
        return recommendations
    
    def print_optimization_summary(self):
        """Print optimization summary"""
        if not self.gpu_info["available"]:
            print("❌ GPU not available")
            return
        
        recommendations = self.get_optimization_recommendations()
        
        print("\n🚀 GPU Optimization Summary:")
        print("=" * 50)
        print(f"📱 GPU: {recommendations['gpu_name']}")
        print(f"💾 Memory: {recommendations['total_memory']:.2f} GB")
        print(f"🔧 Memory Fraction: {recommendations['memory_fraction']*100:.0f}%")
        print(f"📦 Recommended Batch Sizes:")
        print(f"   - Local Training: {recommendations['recommended_batch_sizes']['local_training']}")
        print(f"   - Distillation: {recommendations['recommended_batch_sizes']['distillation']}")
        print(f"   - Evaluation: {recommendations['recommended_batch_sizes']['evaluation']}")
        print(f"👥 Data Workers: {recommendations['recommended_workers']}")
        print(f"🎯 Pseudo Samples/Domain: {recommendations['pseudo_samples_per_domain']}")
        print("=" * 50)

# Global optimizer instance
gpu_optimizer = GPUOptimizer()
