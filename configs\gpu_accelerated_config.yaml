# GPU Accelerated Configuration for D³AFD Multi-Algorithm Framework
# Optimized for maximum GPU utilization and training speed

experiment:
  name: "d3afd_gpu_accelerated"
  device: "cuda"
  seed: 42
  output_dir: "outputs/gpu_accelerated"
  log_level: "INFO"

# Dataset configuration optimized for GPU
data:
  dataset_name: "amazon_reviews"
  data_dir: "data/amazon_reviews"
  domains: ["Electronics", "Books", "Home_and_Kitchen", "Clothing_Shoes_and_Jewelry"]
  max_length: 256  # Reduced for faster processing
  max_seq_length: 256
  train_split: 0.8
  val_split: 0.1
  test_split: 0.1
  num_workers: 12  # Increased for faster data loading
  pin_memory: true
  persistent_workers: true
  prefetch_factor: 4

# Model configuration optimized for GPU
model:
  model_name: "distilbert-base-uncased"
  student_model: "distilbert-base-uncased"
  teacher_model: "distilbert-base-uncased"
  generator_model: "t5-small"
  num_labels: 3
  num_classes: 3
  hidden_size: 768
  teacher_hidden_size: 768
  generator_max_length: 128
  discriminator_hidden_size: 256
  dropout_rate: 0.1
  dropout: 0.1
  freeze_backbone: false

# Training configuration optimized for GPU
training:
  # Algorithm selection: "dkd", "dkdm", "diffdfkd", "diffkd"
  distillation_algorithm: "dkdm"
  
  # GPU-optimized training parameters
  num_rounds: 5
  local_epochs: 3
  distillation_epochs: 3
  batch_size: 48  # Increased for better GPU utilization
  local_batch_size: 48
  distillation_batch_size: 96  # Doubled for efficiency
  learning_rate: 2e-5
  distillation_lr: 1e-4
  local_lr: 1e-5
  personalization_lr: 1e-4
  gradient_clip_norm: 1.0
  weight_decay: 0.01
  optimizer_type: "adamw"
  
  # Optimized pseudo data generation
  pseudo_samples_per_domain: 1500  # Increased for better performance
  cache_refresh_interval: 1  # More aggressive caching
  
  # Loss weights
  contrastive_weight: 0.1
  domain_weight: 0.05
  contrastive_temperature: 0.07
  alpha: 0.7
  beta: 0.3
  temperature: 3.0
  mu: 0.5

# DKDM specific configuration
dkdm:
  num_timesteps: 1000
  batch_ratio: 0.4
  cache_dir: "cache/dkdm"
  distill_weight: 1.0

# DiffDFKD specific configuration  
diffdfkd:
  t5_model: "t5-base"
  guided_scale: 7.0
  inference_steps: 50
  oh_weight: 1.0
  bn_weight: 0.1
  adv_weight: 1.0

# DiffKD specific configuration
diffkd:
  inference_steps: 5
  num_train_timesteps: 1000
  use_ae: true
  ae_channels: 384
  tau: 1.0

# Federated learning configuration
federated:
  num_clients: 4
  clients_per_round: 4
  client_selection: "all"
  data_distribution: "domain_specific"
  alpha: 0.5

# Dynamic Domain Descriptor configuration
domain_descriptor:
  embedding_dim: 256
  num_prototypes: 10
  temperature: 0.1
  update_frequency: 1

# Text generation configuration optimized for speed
text_generation:
  max_length: 128  # Reduced for faster generation
  temperature: 0.9  # Optimized for speed
  do_sample: true
  num_return_sequences: 1
  batch_size: 16  # Added batch processing
  
  # Domain-specific generation prompts
  domain_prompts:
    Electronics:
      - "Generate a review for an electronic device:"
      - "Write a product review for electronics:"
      - "Create a customer review for a gadget:"
    Books:
      - "Generate a book review:"
      - "Write a review for a book:"
      - "Create a customer review for literature:"
    Home_and_Kitchen:
      - "Generate a review for a home appliance:"
      - "Write a review for kitchen equipment:"
      - "Create a customer review for household items:"
    Clothing_Shoes_and_Jewelry:
      - "Generate a review for clothing:"
      - "Write a review for fashion items:"
      - "Create a customer review for accessories:"

# Evaluation configuration
evaluation:
  metrics: ["accuracy", "f1_macro", "f1_weighted", "precision", "recall"]
  eval_frequency: 2
  save_best_model: true
  early_stopping_patience: 5
  batch_size: 64  # Larger batch for faster evaluation

# Advanced memory management for GPU
memory:
  enable_memory_management: true
  max_memory_usage: 0.90  # Aggressive memory usage
  cleanup_frequency: 1  # Clean up every batch
  force_cleanup_epochs: true
  enable_gradient_checkpointing: true
  mixed_precision: true  # Enable mixed precision training

# GPU-specific optimizations
gpu_optimizations:
  enable_cudnn_benchmark: true
  enable_tf32: true
  enable_flash_attention: true
  enable_memory_efficient_attention: true
  pin_memory: true
  non_blocking: true
  compile_model: false  # Set to true for PyTorch 2.0+

# Logging and monitoring optimized for performance
logging:
  log_frequency: 20  # Reduced frequency for speed
  save_logs: true
  tensorboard: false  # Disabled for speed
  wandb:
    enabled: false  # Disabled for speed
    project: "d3afd_gpu_accelerated"
    entity: "your_wandb_entity"

# Reproducibility
reproducibility:
  deterministic: false  # Disabled for speed
  benchmark: true  # Enabled for speed

# Performance monitoring
performance:
  enable_profiling: false
  profile_memory: true
  profile_compute: false
  log_memory_usage: true
  memory_log_frequency: 10
