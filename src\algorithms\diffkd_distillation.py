"""
DiffKD (Diffusion-based Knowledge Distillation) Implementation for D³AFD
Uses diffusion models for feature-level knowledge distillation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, List, Optional, Tuple, Any
import logging
import math

from ..models.base_models import StudentModel
from ..utils.memory_manager import get_memory_manager, memory_cleanup_decorator
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DDIMScheduler:
    """DDIM Scheduler for diffusion process"""
    
    def __init__(self, num_train_timesteps=1000, beta_schedule="linear", clip_sample=False):
        self.num_train_timesteps = num_train_timesteps
        self.clip_sample = clip_sample
        
        # Generate beta schedule
        if beta_schedule == "linear":
            self.betas = torch.linspace(0.0001, 0.02, num_train_timesteps)
        else:
            raise ValueError(f"Unknown beta schedule: {beta_schedule}")
        
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        
        # Set timesteps
        self.timesteps = torch.arange(num_train_timesteps)
    
    def add_noise(self, original_samples, noise, timesteps):
        """Add noise to samples according to timesteps"""
        alphas_cumprod = self.alphas_cumprod.to(original_samples.device)
        timesteps = timesteps.to(original_samples.device)

        # Clamp timesteps to valid range
        timesteps = torch.clamp(timesteps, 0, len(alphas_cumprod) - 1)

        sqrt_alpha_prod = alphas_cumprod[timesteps] ** 0.5
        sqrt_one_minus_alpha_prod = (1 - alphas_cumprod[timesteps]) ** 0.5
        
        # Reshape for broadcasting
        sqrt_alpha_prod = sqrt_alpha_prod.flatten()
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()
        
        while len(sqrt_alpha_prod.shape) < len(original_samples.shape):
            sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)
            sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)
        
        noisy_samples = sqrt_alpha_prod * original_samples + sqrt_one_minus_alpha_prod * noise
        return noisy_samples
    
    def step(self, model_output, timestep, sample, eta=0.0):
        """Perform one denoising step"""
        # Simplified DDIM step
        prev_timestep = timestep - self.num_train_timesteps // 50

        # Clamp timesteps to valid range
        timestep = torch.clamp(timestep, 0, len(self.alphas_cumprod) - 1)
        prev_timestep = torch.clamp(prev_timestep, 0, len(self.alphas_cumprod) - 1)

        alpha_prod_t = self.alphas_cumprod[timestep]
        alpha_prod_t_prev = self.alphas_cumprod[prev_timestep] if prev_timestep >= 0 else torch.tensor(1.0)
        
        beta_prod_t = 1 - alpha_prod_t
        
        # Compute predicted original sample
        pred_original_sample = (sample - beta_prod_t ** 0.5 * model_output) / alpha_prod_t ** 0.5
        
        # Compute coefficients for pred_original_sample and current sample
        pred_sample_direction = (1 - alpha_prod_t_prev) ** 0.5 * model_output
        prev_sample = alpha_prod_t_prev ** 0.5 * pred_original_sample + pred_sample_direction
        
        return {"prev_sample": prev_sample}
    
    def set_timesteps(self, num_inference_steps):
        """Set timesteps for inference"""
        step_ratio = self.num_train_timesteps // num_inference_steps
        timesteps = (torch.arange(0, num_inference_steps) * step_ratio).round()
        self.timesteps = timesteps.long()


class DiffusionModel(nn.Module):
    """Diffusion model for noise prediction"""
    
    def __init__(self, channels_in, kernel_size=3):
        super().__init__()
        self.channels_in = channels_in
        self.time_embedding = nn.Embedding(1280, channels_in)
        
        if kernel_size == 3:
            self.pred = nn.Sequential(
                nn.Conv1d(channels_in, channels_in * 2, 3, padding=1),
                nn.BatchNorm1d(channels_in * 2),
                nn.ReLU(inplace=True),
                nn.Conv1d(channels_in * 2, channels_in, 3, padding=1),
                nn.BatchNorm1d(channels_in),
                nn.ReLU(inplace=True),
                nn.Conv1d(channels_in, channels_in, 1),
                nn.BatchNorm1d(channels_in)
            )
        else:
            self.pred = nn.Sequential(
                nn.Linear(channels_in, channels_in * 4),
                nn.BatchNorm1d(channels_in * 4),
                nn.ReLU(inplace=True),
                nn.Linear(channels_in * 4, channels_in),
                nn.BatchNorm1d(channels_in),
                nn.ReLU(inplace=True),
                nn.Linear(channels_in, channels_in)
            )
    
    def forward(self, noisy_sample, timestep):
        """Predict noise in the sample"""
        if timestep.dtype != torch.long:
            timestep = timestep.long()
        
        # Add time embedding
        time_emb = self.time_embedding(timestep)
        
        # Handle different input shapes
        if len(noisy_sample.shape) == 2:  # [batch, features]
            feat = noisy_sample + time_emb
            return self.pred(feat)
        elif len(noisy_sample.shape) == 3:  # [batch, seq_len, features]
            time_emb = time_emb.unsqueeze(1).expand(-1, noisy_sample.shape[1], -1)
            feat = noisy_sample + time_emb
            # Transpose for Conv1d: [batch, features, seq_len]
            feat = feat.transpose(1, 2)
            output = self.pred(feat)
            # Transpose back: [batch, seq_len, features]
            return output.transpose(1, 2)
        else:
            raise ValueError(f"Unsupported input shape: {noisy_sample.shape}")


class NoiseAdapter(nn.Module):
    """Adapter to determine noise level for features"""
    
    def __init__(self, channels, kernel_size=3):
        super().__init__()
        if kernel_size == 3:
            self.feat = nn.Sequential(
                nn.AdaptiveAvgPool1d(1),
                nn.Flatten(),
                nn.Linear(channels, channels // 4),
                nn.ReLU(inplace=True),
                nn.Linear(channels // 4, channels),
                nn.ReLU(inplace=True)
            )
        else:
            self.feat = nn.Sequential(
                nn.Linear(channels, channels * 2),
                nn.BatchNorm1d(channels * 2),
                nn.ReLU(inplace=True),
                nn.Linear(channels * 2, channels),
                nn.BatchNorm1d(channels)
            )
        
        self.pred = nn.Linear(channels, 2)
    
    def forward(self, x):
        """Predict noise adaptation factor"""
        if len(x.shape) == 3:  # [batch, seq_len, features]
            x = x.mean(dim=1)  # Average over sequence
        
        x = self.feat(x)
        x = self.pred(x).softmax(1)[:, 0]  # Get first component
        return x


class AutoEncoder(nn.Module):
    """AutoEncoder for feature compression"""
    
    def __init__(self, channels, latent_channels):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(channels, latent_channels),
            nn.BatchNorm1d(latent_channels),
            nn.ReLU(inplace=True)
        )
        self.decoder = nn.Sequential(
            nn.Linear(latent_channels, channels)
        )
    
    def forward(self, x):
        """Encode and decode features"""
        hidden = self.encoder(x)
        reconstructed = self.decoder(hidden)
        return hidden, reconstructed


class DiffKD(nn.Module):
    """DiffKD module for feature-level knowledge distillation"""
    
    def __init__(self, student_channels, teacher_channels, kernel_size=3,
                 inference_steps=5, num_train_timesteps=1000, use_ae=False, ae_channels=None):
        super().__init__()
        self.use_ae = use_ae
        self.diffusion_inference_steps = inference_steps
        
        # AutoEncoder for teacher feature compression
        if use_ae:
            if ae_channels is None:
                ae_channels = teacher_channels // 2
            self.ae = AutoEncoder(teacher_channels, ae_channels)
            teacher_channels = ae_channels
        
        # Transform student features to teacher dimension
        self.trans = nn.Linear(student_channels, teacher_channels)
        
        # Diffusion model for noise prediction
        self.model = DiffusionModel(channels_in=teacher_channels, kernel_size=kernel_size)
        self.scheduler = DDIMScheduler(num_train_timesteps=num_train_timesteps, clip_sample=False)
        self.noise_adapter = NoiseAdapter(teacher_channels, kernel_size)
        
        # Projection layer
        self.proj = nn.Sequential(
            nn.Linear(teacher_channels, teacher_channels),
            nn.BatchNorm1d(teacher_channels)
        )
    
    def forward(self, student_feat, teacher_feat):
        """Forward pass for DiffKD"""
        # Project student features
        student_feat = self.trans(student_feat)
        
        # Use autoencoder on teacher features if enabled
        rec_loss = None
        if self.use_ae:
            hidden_t_feat, rec_t_feat = self.ae(teacher_feat)
            rec_loss = F.mse_loss(teacher_feat, rec_t_feat)
            teacher_feat = hidden_t_feat.detach()
        
        # Denoise student features using diffusion pipeline
        refined_feat = self.diffusion_pipeline(student_feat)
        refined_feat = self.proj(refined_feat)
        
        # Train diffusion model
        ddim_loss = self.ddim_loss(teacher_feat)
        
        return refined_feat, teacher_feat, ddim_loss, rec_loss
    
    def diffusion_pipeline(self, feat):
        """Diffusion denoising pipeline"""
        batch_size = feat.shape[0]
        device = feat.device
        dtype = feat.dtype
        
        # Initialize with noise or use noise adapter
        if self.noise_adapter is not None:
            noise = torch.randn_like(feat)
            timesteps = self.noise_adapter(feat)
            # Convert timesteps to integer indices
            timesteps = (timesteps * (self.scheduler.num_train_timesteps - 1)).long()
            image = self.scheduler.add_noise(feat, noise, timesteps)
        else:
            image = feat
        
        # Set timesteps for inference
        self.scheduler.set_timesteps(self.diffusion_inference_steps * 2)
        
        # Denoising loop
        for t in self.scheduler.timesteps[len(self.scheduler.timesteps)//2:]:
            t_tensor = torch.full((batch_size,), t, device=device, dtype=torch.long)
            noise_pred = self.model(image, t_tensor)
            
            # Perform denoising step
            image = self.scheduler.step(noise_pred, t, image)['prev_sample']
        
        return image
    
    def ddim_loss(self, gt_feat):
        """Compute DDIM training loss"""
        # Sample noise
        noise = torch.randn_like(gt_feat)
        batch_size = gt_feat.shape[0]
        
        # Sample random timesteps
        timesteps = torch.randint(0, self.scheduler.num_train_timesteps, 
                                (batch_size,), device=gt_feat.device).long()
        
        # Add noise to features (forward diffusion)
        noisy_features = self.scheduler.add_noise(gt_feat, noise, timesteps)
        
        # Predict noise
        noise_pred = self.model(noisy_features, timesteps)
        
        # Compute MSE loss
        loss = F.mse_loss(noise_pred, noise)
        return loss


class DiffKDDistillationTrainer:
    """
    DiffKD-based distillation trainer for D³AFD framework
    Uses diffusion models for feature-level knowledge distillation
    """
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.experiment.device)
        
        # DiffKD specific parameters
        self.inference_steps = config.diffkd.inference_steps if hasattr(config, 'diffkd') else 5
        self.num_train_timesteps = config.diffkd.num_train_timesteps if hasattr(config, 'diffkd') else 1000
        self.use_ae = config.diffkd.use_ae if hasattr(config, 'diffkd') else True
        self.ae_channels = config.diffkd.ae_channels if hasattr(config, 'diffkd') else None
        self.tau = config.diffkd.tau if hasattr(config, 'diffkd') else 1.0
        
        # Feature extraction modules
        self.diffkd_modules = {}
        
        logger.info("DiffKD distillation trainer initialized")
    
    def setup_diffkd_modules(self, student_model: nn.Module, teacher_models: Dict[str, nn.Module]):
        """Setup DiffKD modules for feature distillation"""
        # Get feature dimensions (simplified for text models)
        student_hidden_size = self.config.model.hidden_size
        teacher_hidden_size = self.config.model.hidden_size
        
        # Create DiffKD modules for different layers
        kernel_sizes = [3, 1]  # Feature and logits distillation
        layer_names = ['features', 'logits']
        
        for layer_name, kernel_size in zip(layer_names, kernel_sizes):
            self.diffkd_modules[layer_name] = DiffKD(
                student_channels=student_hidden_size,
                teacher_channels=teacher_hidden_size,
                kernel_size=kernel_size,
                inference_steps=self.inference_steps,
                num_train_timesteps=self.num_train_timesteps,
                use_ae=(kernel_size != 1) and self.use_ae,
                ae_channels=self.ae_channels
            ).to(self.device)
        
        logger.info(f"Setup {len(self.diffkd_modules)} DiffKD modules")
    
    @memory_cleanup_decorator
    def train_student_model(self, student_model: nn.Module, teacher_models: Dict[str, nn.Module],
                          pseudo_dataloader: DataLoader, num_epochs: int = 3) -> Dict[str, float]:
        """
        Train student model using DiffKD feature-level distillation
        
        Args:
            student_model: Student model to train
            teacher_models: Dictionary of teacher models
            pseudo_dataloader: DataLoader for pseudo samples
            num_epochs: Number of training epochs
            
        Returns:
            Dictionary of training metrics
        """
        logger.info("Starting DiffKD feature-level distillation training...")
        
        # Setup DiffKD modules
        self.setup_diffkd_modules(student_model, teacher_models)
        
        # Setup optimizer (include DiffKD modules)
        all_params = list(student_model.parameters())
        for module in self.diffkd_modules.values():
            all_params.extend(list(module.parameters()))
        
        optimizer = torch.optim.AdamW(
            all_params,
            lr=self.config.training.distillation_lr
        )
        
        student_model.train()
        for module in self.diffkd_modules.values():
            module.train()
        
        total_metrics = {
            'total_loss': 0,
            'kd_loss': 0,
            'diff_loss': 0,
            'ae_loss': 0
        }
        num_batches = 0
        
        for epoch in range(num_epochs):
            epoch_metrics = {key: 0 for key in total_metrics.keys()}
            epoch_batches = 0
            
            for batch in pseudo_dataloader:
                try:
                    # Move batch to device
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    
                    # Get student features and outputs
                    student_outputs = student_model(input_ids, attention_mask=attention_mask)
                    student_logits = student_outputs.logits if hasattr(student_outputs, 'logits') else student_outputs
                    
                    # Get ensemble teacher outputs
                    teacher_logits_list = []
                    for teacher_model in teacher_models.values():
                        teacher_model.eval()
                        with torch.no_grad():
                            teacher_outputs = teacher_model(input_ids, attention_mask=attention_mask)
                            teacher_logits = teacher_outputs.logits if hasattr(teacher_outputs, 'logits') else teacher_outputs
                            teacher_logits_list.append(teacher_logits)
                    
                    # Average teacher outputs
                    ensemble_teacher_logits = torch.stack(teacher_logits_list).mean(dim=0)
                    
                    # Apply DiffKD to features and logits
                    total_loss = 0
                    diff_loss_total = 0
                    ae_loss_total = 0
                    
                    # Feature-level distillation (simplified - using final hidden states)
                    if hasattr(student_outputs, 'hidden_states') and student_outputs.hidden_states is not None:
                        student_features = student_outputs.hidden_states[-1].mean(dim=1)  # Average over sequence
                    else:
                        student_features = student_logits
                    
                    teacher_features = ensemble_teacher_logits
                    
                    # Apply DiffKD
                    refined_features, teacher_features_processed, diff_loss, ae_loss = self.diffkd_modules['features'](
                        student_features, teacher_features
                    )
                    
                    # Feature distillation loss
                    kd_loss = F.mse_loss(refined_features, teacher_features_processed.detach())
                    
                    # Logits distillation loss
                    logits_kd_loss = F.kl_div(
                        F.log_softmax(student_logits / self.tau, dim=1),
                        F.softmax(ensemble_teacher_logits / self.tau, dim=1),
                        reduction='batchmean'
                    ) * (self.tau ** 2)
                    
                    # Combine losses
                    total_loss = kd_loss + logits_kd_loss + diff_loss
                    if ae_loss is not None:
                        total_loss += ae_loss
                        ae_loss_total += ae_loss.item()
                    
                    diff_loss_total += diff_loss.item()
                    
                    # Backward pass
                    optimizer.zero_grad()
                    total_loss.backward()
                    optimizer.step()
                    
                    # Update metrics
                    epoch_metrics['total_loss'] += total_loss.item()
                    epoch_metrics['kd_loss'] += (kd_loss.item() + logits_kd_loss.item())
                    epoch_metrics['diff_loss'] += diff_loss_total
                    epoch_metrics['ae_loss'] += ae_loss_total
                    epoch_batches += 1
                    
                except Exception as e:
                    logger.error(f"Error in DiffKD training batch: {e}")
                    continue
            
            # Average epoch metrics
            for key in epoch_metrics:
                if epoch_batches > 0:
                    epoch_metrics[key] /= epoch_batches
                    total_metrics[key] += epoch_metrics[key]
            
            num_batches += 1
            
            logger.info(f"DiffKD Epoch {epoch+1}/{num_epochs}, "
                       f"Total Loss: {epoch_metrics['total_loss']:.4f}, "
                       f"KD Loss: {epoch_metrics['kd_loss']:.4f}, "
                       f"Diff Loss: {epoch_metrics['diff_loss']:.4f}")
        
        # Average total metrics
        for key in total_metrics:
            if num_batches > 0:
                total_metrics[key] /= num_batches
        
        return {
            'diffkd_loss': total_metrics['total_loss'],
            'kd_loss': total_metrics['kd_loss'],
            'diff_loss': total_metrics['diff_loss'],
            'ae_loss': total_metrics['ae_loss'],
            'algorithm': 'diffkd',
            'epochs': num_epochs
        }
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """Get information about DiffKD algorithm"""
        return {
            "name": "DiffKD",
            "description": "Diffusion-based Knowledge Distillation for Feature Learning",
            "supports_feature_distillation": True,
            "inference_steps": self.inference_steps,
            "num_train_timesteps": self.num_train_timesteps,
            "use_autoencoder": self.use_ae,
            "temperature": self.tau
        }
