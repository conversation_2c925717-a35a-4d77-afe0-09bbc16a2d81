2025-08-06 11:30:36,728 - quick_test_main - INFO - Quick test started with device: cuda
2025-08-06 11:30:36,728 - quick_test_main - INFO - Main log file: logs/quick_test_all_algorithms_20250806_113036.log
2025-08-06 11:30:36,728 - quick_test_main - INFO - Starting tests for all algorithms
2025-08-06 11:30:36,728 - quick_test_main - INFO - Testing algorithm: dkd
2025-08-06 11:30:40,178 - quick_test_dkd - INFO - Logging to file: logs/quick_test_dkd_20250806_113040.log
2025-08-06 11:30:40,178 - d3afd_train - INFO - Starting D³AFD experiment with dkd algorithm
2025-08-06 11:30:40,178 - d3afd_train - INFO - Output directory: outputs/quick_test_dkd/dkd_20250806_113040
2025-08-06 11:30:40,178 - d3afd_train - INFO - Loading Amazon Reviews dataset...
2025-08-06 11:30:40,943 - src.data.amazon_dataset - INFO - Loading Amazon Reviews dataset...
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Config type: <class 'src.utils.config.Config'>
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Config.data available: True
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Config.data.domains: ['Electronics', 'Books', 'Home_and_Kitchen', 'Clothing_Shoes_and_Jewelry']
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Using domains: ['Electronics', 'Books', 'Home_and_Kitchen', 'Clothing_Shoes_and_Jewelry']
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Samples per domain: 100
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Loaded 400 samples across 4 domains
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 400 samples
2025-08-06 11:30:40,944 - d3afd_train - INFO - Setting up 4 clients for domains: ['Electronics', 'Books', 'Home_and_Kitchen', 'Clothing_Shoes_and_Jewelry']
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Getting domain data for 'Electronics'
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Total samples in dataset: 400
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Available domains: ['Clothing_Shoes_and_Jewelry', 'Electronics', 'Books', 'Home_and_Kitchen']
2025-08-06 11:30:40,944 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Electronics
2025-08-06 11:30:41,690 - src.federated.client - INFO - Client client_0_Electronics: Creating dataloader from 100 samples
2025-08-06 11:30:41,691 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 11:30:44,712 - src.federated.client - INFO - Client client_0_Electronics initialized with domain: Electronics
2025-08-06 11:30:44,713 - d3afd_train - INFO - Created client client_0_Electronics with 100 samples
2025-08-06 11:30:44,713 - src.data.amazon_dataset - INFO - Getting domain data for 'Books'
2025-08-06 11:30:44,713 - src.data.amazon_dataset - INFO - Total samples in dataset: 400
2025-08-06 11:30:44,713 - src.data.amazon_dataset - INFO - Available domains: ['Clothing_Shoes_and_Jewelry', 'Electronics', 'Books', 'Home_and_Kitchen']
2025-08-06 11:30:44,713 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Books
2025-08-06 11:30:45,463 - src.federated.client - INFO - Client client_1_Books: Creating dataloader from 100 samples
2025-08-06 11:30:45,463 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 11:30:46,056 - src.federated.client - INFO - Client client_1_Books initialized with domain: Books
2025-08-06 11:30:46,056 - d3afd_train - INFO - Created client client_1_Books with 100 samples
2025-08-06 11:30:46,056 - src.data.amazon_dataset - INFO - Getting domain data for 'Home_and_Kitchen'
2025-08-06 11:30:46,057 - src.data.amazon_dataset - INFO - Total samples in dataset: 400
2025-08-06 11:30:46,057 - src.data.amazon_dataset - INFO - Available domains: ['Clothing_Shoes_and_Jewelry', 'Electronics', 'Books', 'Home_and_Kitchen']
2025-08-06 11:30:46,057 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Home_and_Kitchen
2025-08-06 11:30:46,801 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Creating dataloader from 100 samples
2025-08-06 11:30:46,801 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 11:30:47,420 - src.federated.client - INFO - Client client_2_Home_and_Kitchen initialized with domain: Home_and_Kitchen
2025-08-06 11:30:47,420 - d3afd_train - INFO - Created client client_2_Home_and_Kitchen with 100 samples
2025-08-06 11:30:47,420 - src.data.amazon_dataset - INFO - Getting domain data for 'Clothing_Shoes_and_Jewelry'
2025-08-06 11:30:47,420 - src.data.amazon_dataset - INFO - Total samples in dataset: 400
2025-08-06 11:30:47,420 - src.data.amazon_dataset - INFO - Available domains: ['Clothing_Shoes_and_Jewelry', 'Electronics', 'Books', 'Home_and_Kitchen']
2025-08-06 11:30:47,421 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Clothing_Shoes_and_Jewelry
2025-08-06 11:30:48,154 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Creating dataloader from 100 samples
2025-08-06 11:30:48,154 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 11:30:48,753 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry initialized with domain: Clothing_Shoes_and_Jewelry
2025-08-06 11:30:48,753 - d3afd_train - INFO - Created client client_3_Clothing_Shoes_and_Jewelry with 100 samples
2025-08-06 11:30:48,754 - d3afd_train - INFO - Initializing federated server with dkd algorithm...
2025-08-06 11:30:50,132 - src.models.text_generator - INFO - Loading pretrained T5 model: t5-small
2025-08-06 11:30:52,181 - src.models.text_generator - INFO - T5生成器支持动态域描述符生成
2025-08-06 11:30:52,182 - src.federated.server - INFO - Initialized D³AFD server with dkd distillation algorithm
2025-08-06 11:30:52,182 - src.federated.server - INFO - Federated server initialized
2025-08-06 11:30:52,182 - src.utils.metrics - INFO - Metrics tracker initialized for dkd algorithm
2025-08-06 11:30:52,182 - d3afd_train - INFO - Starting federated training...
2025-08-06 11:30:52,182 - d3afd_train - INFO - 
==================================================
2025-08-06 11:30:52,182 - d3afd_train - INFO - Round 1/3
2025-08-06 11:30:52,183 - d3afd_train - INFO - Algorithm: dkd
2025-08-06 11:30:52,183 - d3afd_train - INFO - ==================================================
2025-08-06 11:30:52,183 - d3afd_train - INFO - Phase 1: Distributing global model to clients...
2025-08-06 11:30:52,183 - src.federated.server - INFO - Distributing global model to all clients...
2025-08-06 11:30:52,184 - src.federated.client - INFO - Client client_0_Electronics: Updating local model with global state.
2025-08-06 11:30:52,190 - src.federated.client - INFO - Client client_1_Books: Updating local model with global state.
2025-08-06 11:30:52,195 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Updating local model with global state.
2025-08-06 11:30:52,201 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Updating local model with global state.
2025-08-06 11:30:52,206 - d3afd_train - INFO - Phase 2: Local training on clients...
2025-08-06 11:30:52,206 - d3afd_train - INFO - Training client client_0_Electronics...
2025-08-06 11:30:52,206 - src.federated.client - INFO - Client client_0_Electronics: Training local teacher for 2 epochs
2025-08-06 11:30:57,052 - src.federated.client - INFO - Client client_0_Electronics - Train Epoch 1/2: Loss: 1.0688, Accuracy: 0.4000
2025-08-06 11:31:01,334 - src.federated.client - INFO - Client client_0_Electronics - Train Epoch 2/2: Loss: 0.8609, Accuracy: 0.9200
2025-08-06 11:31:01,579 - src.federated.client - INFO - Client client_0_Electronics: Local teacher training completed. Final accuracy: 0.6600
2025-08-06 11:31:01,580 - d3afd_train - INFO - Training client client_1_Books...
2025-08-06 11:31:01,580 - src.federated.client - INFO - Client client_1_Books: Training local teacher for 2 epochs
2025-08-06 11:31:05,626 - src.federated.client - INFO - Client client_1_Books - Train Epoch 1/2: Loss: 1.0019, Accuracy: 0.5400
2025-08-06 11:31:09,941 - src.federated.client - INFO - Client client_1_Books - Train Epoch 2/2: Loss: 0.7567, Accuracy: 0.9300
2025-08-06 11:31:10,203 - src.federated.client - INFO - Client client_1_Books: Local teacher training completed. Final accuracy: 0.7350
2025-08-06 11:31:10,204 - d3afd_train - INFO - Training client client_2_Home_and_Kitchen...
2025-08-06 11:31:10,204 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Training local teacher for 2 epochs
2025-08-06 11:31:14,286 - src.federated.client - INFO - Client client_2_Home_and_Kitchen - Train Epoch 1/2: Loss: 1.0630, Accuracy: 0.4200
2025-08-06 11:31:18,621 - src.federated.client - INFO - Client client_2_Home_and_Kitchen - Train Epoch 2/2: Loss: 0.8687, Accuracy: 0.7700
2025-08-06 11:31:18,874 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Local teacher training completed. Final accuracy: 0.5950
2025-08-06 11:31:18,875 - d3afd_train - INFO - Training client client_3_Clothing_Shoes_and_Jewelry...
2025-08-06 11:31:18,876 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Training local teacher for 2 epochs
2025-08-06 11:31:22,970 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry - Train Epoch 1/2: Loss: 1.0931, Accuracy: 0.3900
2025-08-06 11:31:27,305 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry - Train Epoch 2/2: Loss: 0.8883, Accuracy: 0.9000
2025-08-06 11:31:27,541 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Local teacher training completed. Final accuracy: 0.6450
2025-08-06 11:31:27,543 - d3afd_train - INFO - Phase 3: Global distillation...
2025-08-06 11:31:27,544 - src.utils.memory_manager - INFO - GPU Memory before_distillation_round: Allocated: 2.48GB, Reserved: 3.26GB, Free: 11.31GB
2025-08-06 11:31:27,544 - src.federated.server - INFO - Starting distillation round 0
2025-08-06 11:31:27,544 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_0: Allocated: 2.48GB, Reserved: 3.26GB, Free: 11.31GB
2025-08-06 11:31:27,545 - src.federated.server - INFO - 🔄 生成动态域描述符...
2025-08-06 11:31:27,545 - src.federated.server - INFO - 第 0 轮: 生成动态域描述符
2025-08-06 11:31:27,545 - src.utils.memory_manager - INFO - GPU Memory start_dynamic_descriptors_round_0: Allocated: 2.48GB, Reserved: 3.26GB, Free: 11.31GB
2025-08-06 11:31:27,545 - src.federated.server - INFO - 处理客户端 client_0_Electronics 的域描述符...
2025-08-06 11:31:27,712 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 11:31:27,712 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_0_Electronics 生成第 0 轮域描述符
2025-08-06 11:31:27,712 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:31:29,074 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:31:29,075 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_0_Electronics 域描述符生成完成:
2025-08-06 11:31:29,075 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7151
2025-08-06 11:31:29,075 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0023
2025-08-06 11:31:29,076 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.6104
2025-08-06 11:31:29,076 - src.federated.server - INFO - 客户端 client_0_Electronics 域描述符生成完成
2025-08-06 11:31:29,076 - src.federated.server - INFO -   演化趋势: initializing
2025-08-06 11:31:29,076 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:31:29,649 - src.federated.server - INFO - 处理客户端 client_1_Books 的域描述符...
2025-08-06 11:31:29,801 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 11:31:29,801 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_1_Books 生成第 0 轮域描述符
2025-08-06 11:31:29,801 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:31:31,168 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:31:31,169 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_1_Books 域描述符生成完成:
2025-08-06 11:31:31,169 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7767
2025-08-06 11:31:31,169 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0044
2025-08-06 11:31:31,170 - src.models.dynamic_domain_descriptor - INFO -   域密度: 1.9018
2025-08-06 11:31:31,170 - src.federated.server - INFO - 客户端 client_1_Books 域描述符生成完成
2025-08-06 11:31:31,170 - src.federated.server - INFO -   演化趋势: initializing
2025-08-06 11:31:31,170 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:31:31,753 - src.federated.server - INFO - 处理客户端 client_2_Home_and_Kitchen 的域描述符...
2025-08-06 11:31:31,918 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 11:31:31,919 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_2_Home_and_Kitchen 生成第 0 轮域描述符
2025-08-06 11:31:31,919 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:31:33,274 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:31:33,275 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_2_Home_and_Kitchen 域描述符生成完成:
2025-08-06 11:31:33,275 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7222
2025-08-06 11:31:33,275 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0026
2025-08-06 11:31:33,275 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.4933
2025-08-06 11:31:33,275 - src.federated.server - INFO - 客户端 client_2_Home_and_Kitchen 域描述符生成完成
2025-08-06 11:31:33,275 - src.federated.server - INFO -   演化趋势: initializing
2025-08-06 11:31:33,275 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:31:33,866 - src.federated.server - INFO - 处理客户端 client_3_Clothing_Shoes_and_Jewelry 的域描述符...
2025-08-06 11:31:34,017 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 11:31:34,017 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_3_Clothing_Shoes_and_Jewelry 生成第 0 轮域描述符
2025-08-06 11:31:34,017 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:31:35,364 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:31:35,365 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 域描述符生成完成:
2025-08-06 11:31:35,365 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.8859
2025-08-06 11:31:35,365 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0030
2025-08-06 11:31:35,365 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.3031
2025-08-06 11:31:35,366 - src.federated.server - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 域描述符生成完成
2025-08-06 11:31:35,366 - src.federated.server - INFO -   演化趋势: initializing
2025-08-06 11:31:35,366 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:31:35,937 - src.models.text_generator - INFO - 更新域描述符，客户端数量: 4
2025-08-06 11:31:35,938 - src.models.text_generator - INFO - 客户端 client_0_Electronics 第 0 轮域描述符已更新
2025-08-06 11:31:35,938 - src.models.text_generator - INFO - 客户端 client_1_Books 第 0 轮域描述符已更新
2025-08-06 11:31:35,938 - src.models.text_generator - INFO - 客户端 client_2_Home_and_Kitchen 第 0 轮域描述符已更新
2025-08-06 11:31:35,938 - src.models.text_generator - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 第 0 轮域描述符已更新
2025-08-06 11:31:36,471 - src.utils.memory_manager - INFO - GPU Memory end_dynamic_descriptors_round_0: Allocated: 2.48GB, Reserved: 2.80GB, Free: 11.78GB
2025-08-06 11:31:36,471 - src.federated.server - INFO - 动态域描述符生成完成，客户端数量: 4
2025-08-06 11:31:36,471 - src.federated.server - INFO - Round 0: Generating new pseudo data
2025-08-06 11:31:36,471 - src.models.text_generator - INFO - Generating 1000 pseudo samples for domain: Electronics
2025-08-06 11:36:54,810 - src.models.text_generator - INFO - Generating 1000 pseudo samples for domain: Books
2025-08-06 11:42:18,240 - src.models.text_generator - INFO - Generating 1000 pseudo samples for domain: Home_and_Kitchen
2025-08-06 11:47:57,516 - src.models.text_generator - INFO - Generating 1000 pseudo samples for domain: Clothing_Shoes_and_Jewelry
2025-08-06 11:53:31,935 - src.utils.memory_manager - INFO - GPU Memory after_distillation_round: Allocated: 2.48GB, Reserved: 2.80GB, Free: 11.78GB
2025-08-06 11:53:31,935 - d3afd_train - ERROR - Error in round 1: 'Config' object has no attribute 'mixed_domain_samples'
2025-08-06 11:53:31,936 - d3afd_train - INFO - 
==================================================
2025-08-06 11:53:31,937 - d3afd_train - INFO - Round 2/3
2025-08-06 11:53:31,937 - d3afd_train - INFO - Algorithm: dkd
2025-08-06 11:53:31,937 - d3afd_train - INFO - ==================================================
2025-08-06 11:53:31,937 - d3afd_train - INFO - Phase 1: Distributing global model to clients...
2025-08-06 11:53:31,937 - src.federated.server - INFO - Distributing global model to all clients...
2025-08-06 11:53:31,938 - src.federated.client - INFO - Client client_0_Electronics: Updating local model with global state.
2025-08-06 11:53:31,944 - src.federated.client - INFO - Client client_1_Books: Updating local model with global state.
2025-08-06 11:53:31,949 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Updating local model with global state.
2025-08-06 11:53:31,954 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Updating local model with global state.
2025-08-06 11:53:31,961 - d3afd_train - INFO - Phase 2: Local training on clients...
2025-08-06 11:53:31,961 - d3afd_train - INFO - Training client client_0_Electronics...
2025-08-06 11:53:31,961 - src.federated.client - INFO - Client client_0_Electronics: Training local teacher for 2 epochs
2025-08-06 11:53:36,137 - src.federated.client - INFO - Client client_0_Electronics - Train Epoch 1/2: Loss: 1.0559, Accuracy: 0.4600
2025-08-06 11:53:40,504 - src.federated.client - INFO - Client client_0_Electronics - Train Epoch 2/2: Loss: 0.8578, Accuracy: 0.8400
2025-08-06 11:53:40,767 - src.federated.client - INFO - Client client_0_Electronics: Local teacher training completed. Final accuracy: 0.6500
2025-08-06 11:53:40,768 - d3afd_train - INFO - Training client client_1_Books...
2025-08-06 11:53:40,768 - src.federated.client - INFO - Client client_1_Books: Training local teacher for 2 epochs
2025-08-06 11:53:44,880 - src.federated.client - INFO - Client client_1_Books - Train Epoch 1/2: Loss: 0.9748, Accuracy: 0.5700
2025-08-06 11:53:49,233 - src.federated.client - INFO - Client client_1_Books - Train Epoch 2/2: Loss: 0.7274, Accuracy: 0.9400
2025-08-06 11:53:49,475 - src.federated.client - INFO - Client client_1_Books: Local teacher training completed. Final accuracy: 0.7550
2025-08-06 11:53:49,476 - d3afd_train - INFO - Training client client_2_Home_and_Kitchen...
2025-08-06 11:53:49,476 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Training local teacher for 2 epochs
2025-08-06 11:53:53,611 - src.federated.client - INFO - Client client_2_Home_and_Kitchen - Train Epoch 1/2: Loss: 1.0964, Accuracy: 0.3700
2025-08-06 11:53:58,003 - src.federated.client - INFO - Client client_2_Home_and_Kitchen - Train Epoch 2/2: Loss: 0.8713, Accuracy: 0.8100
2025-08-06 11:53:58,239 - src.federated.client - INFO - Client client_2_Home_and_Kitchen: Local teacher training completed. Final accuracy: 0.5900
2025-08-06 11:53:58,240 - d3afd_train - INFO - Training client client_3_Clothing_Shoes_and_Jewelry...
2025-08-06 11:53:58,240 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Training local teacher for 2 epochs
2025-08-06 11:54:02,410 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry - Train Epoch 1/2: Loss: 1.0524, Accuracy: 0.4200
2025-08-06 11:54:06,824 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry - Train Epoch 2/2: Loss: 0.8500, Accuracy: 0.8500
2025-08-06 11:54:07,066 - src.federated.client - INFO - Client client_3_Clothing_Shoes_and_Jewelry: Local teacher training completed. Final accuracy: 0.6350
2025-08-06 11:54:07,067 - d3afd_train - INFO - Phase 3: Global distillation...
2025-08-06 11:54:07,068 - src.utils.memory_manager - INFO - GPU Memory before_distillation_round: Allocated: 2.49GB, Reserved: 3.15GB, Free: 11.42GB
2025-08-06 11:54:07,068 - src.federated.server - INFO - Starting distillation round 1
2025-08-06 11:54:07,068 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_1: Allocated: 2.49GB, Reserved: 3.15GB, Free: 11.42GB
2025-08-06 11:54:07,068 - src.federated.server - INFO - 🔄 生成动态域描述符...
2025-08-06 11:54:07,068 - src.federated.server - INFO - 第 1 轮: 生成动态域描述符
2025-08-06 11:54:07,069 - src.utils.memory_manager - INFO - GPU Memory start_dynamic_descriptors_round_1: Allocated: 2.49GB, Reserved: 3.15GB, Free: 11.42GB
2025-08-06 11:54:07,069 - src.federated.server - INFO - 处理客户端 client_0_Electronics 的域描述符...
2025-08-06 11:54:07,225 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_0_Electronics 生成第 1 轮域描述符
2025-08-06 11:54:07,225 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:54:08,582 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:54:08,583 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_0_Electronics 域描述符生成完成:
2025-08-06 11:54:08,583 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7079
2025-08-06 11:54:08,583 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0025
2025-08-06 11:54:08,583 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.5554
2025-08-06 11:54:08,583 - src.federated.server - INFO - 客户端 client_0_Electronics 域描述符生成完成
2025-08-06 11:54:08,583 - src.federated.server - INFO -   演化趋势: stabilizing
2025-08-06 11:54:08,583 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:54:09,168 - src.federated.server - INFO - 处理客户端 client_1_Books 的域描述符...
2025-08-06 11:54:09,328 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_1_Books 生成第 1 轮域描述符
2025-08-06 11:54:09,328 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:54:10,684 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:54:10,685 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_1_Books 域描述符生成完成:
2025-08-06 11:54:10,685 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7765
2025-08-06 11:54:10,685 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0050
2025-08-06 11:54:10,685 - src.models.dynamic_domain_descriptor - INFO -   域密度: 1.7875
2025-08-06 11:54:10,685 - src.federated.server - INFO - 客户端 client_1_Books 域描述符生成完成
2025-08-06 11:54:10,686 - src.federated.server - INFO -   演化趋势: stabilizing
2025-08-06 11:54:10,686 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:54:11,257 - src.federated.server - INFO - 处理客户端 client_2_Home_and_Kitchen 的域描述符...
2025-08-06 11:54:11,416 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_2_Home_and_Kitchen 生成第 1 轮域描述符
2025-08-06 11:54:11,416 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:54:12,774 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:54:12,775 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_2_Home_and_Kitchen 域描述符生成完成:
2025-08-06 11:54:12,775 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.7007
2025-08-06 11:54:12,776 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0025
2025-08-06 11:54:12,776 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.5429
2025-08-06 11:54:12,776 - src.federated.server - INFO - 客户端 client_2_Home_and_Kitchen 域描述符生成完成
2025-08-06 11:54:12,776 - src.federated.server - INFO -   演化趋势: stabilizing
2025-08-06 11:54:12,776 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:54:13,363 - src.federated.server - INFO - 处理客户端 client_3_Clothing_Shoes_and_Jewelry 的域描述符...
2025-08-06 11:54:13,530 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_3_Clothing_Shoes_and_Jewelry 生成第 1 轮域描述符
2025-08-06 11:54:13,530 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 11:54:14,905 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 11:54:14,906 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 域描述符生成完成:
2025-08-06 11:54:14,906 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.8941
2025-08-06 11:54:14,906 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0034
2025-08-06 11:54:14,907 - src.models.dynamic_domain_descriptor - INFO -   域密度: 2.1673
2025-08-06 11:54:14,907 - src.federated.server - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 域描述符生成完成
2025-08-06 11:54:14,907 - src.federated.server - INFO -   演化趋势: stabilizing
2025-08-06 11:54:14,907 - src.federated.server - INFO -   稳定性: 0.5000
2025-08-06 11:54:15,483 - src.models.text_generator - INFO - 更新域描述符，客户端数量: 4
2025-08-06 11:54:15,483 - src.models.text_generator - INFO - 客户端 client_0_Electronics 第 1 轮域描述符已更新
2025-08-06 11:54:15,483 - src.models.text_generator - INFO - 客户端 client_1_Books 第 1 轮域描述符已更新
2025-08-06 11:54:15,483 - src.models.text_generator - INFO - 客户端 client_2_Home_and_Kitchen 第 1 轮域描述符已更新
2025-08-06 11:54:15,483 - src.models.text_generator - INFO - 客户端 client_3_Clothing_Shoes_and_Jewelry 第 1 轮域描述符已更新
2025-08-06 11:54:16,015 - src.utils.memory_manager - INFO - GPU Memory end_dynamic_descriptors_round_1: Allocated: 2.49GB, Reserved: 2.76GB, Free: 11.82GB
2025-08-06 11:54:16,015 - src.federated.server - INFO - 动态域描述符生成完成，客户端数量: 4
2025-08-06 11:54:16,016 - src.federated.server - INFO - Round 1: Generating new pseudo data
2025-08-06 11:54:16,016 - src.models.text_generator - INFO - Generating 1000 pseudo samples for domain: Electronics
