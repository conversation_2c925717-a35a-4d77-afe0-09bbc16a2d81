#!/usr/bin/env python3
"""
Quick Test Script for D³AFD Multi-Algorithm Framework
Test different distillation algorithms with minimal setup
"""

import os
import sys
import argparse
import logging
import yaml
import torch
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def load_and_merge_configs(algorithm):
    """Load base config and merge with algorithm-specific config"""
    
    def deep_update(d, u):
        for k, v in u.items():
            if isinstance(v, dict):
                d[k] = deep_update(d.get(k, {}), v)
            else:
                d[k] = v
        return d

    # Load base configuration
    base_config_path = 'configs/d3afd_multi_algorithm.yaml'
    if not os.path.exists(base_config_path):
        raise FileNotFoundError(f"Base config file not found at {base_config_path}")
        
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Load algorithm-specific configuration
    algo_config_path = f'configs/quick_tests/{algorithm}.yaml'
    if os.path.exists(algo_config_path):
        with open(algo_config_path, 'r') as f:
            algo_config = yaml.safe_load(f)
        # Deep merge the algorithm config into the base config, if it's not empty
        if algo_config:
            config = deep_update(config, algo_config)

    # Apply quick test overrides
    config['experiment']['name'] = f'd3afd_quick_test_{algorithm}'
    config['experiment']['output_dir'] = f'outputs/quick_test_{algorithm}'
    config['training']['num_rounds'] = 3
    config['training']['local_epochs'] = 2
    config['training']['distillation_epochs'] = 2
    config['data']['samples_per_client'] = 100
    config['logging']['tensorboard'] = False
    config['logging']['wandb']['enabled'] = False

    return config

def run_quick_test(algorithm):
    """Run a quick test with the specified algorithm"""
    print(f"\n{'='*60}")
    print(f"Quick Test: {algorithm.upper()} Algorithm")
    print(f"{'='*60}")
    
    try:
        # Load configs
        config_dict = load_and_merge_configs(algorithm)
        
        # Force reload modules to ensure latest changes are loaded
        import importlib
        import sys

        modules_to_reload = [
            'src.federated.client',
            'src.data.amazon_dataset',
            'src.utils.config',
            'train_d3afd_multi_algorithm'
        ]

        for module_name in modules_to_reload:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])

        # Import and run
        from train_d3afd_multi_algorithm import run_experiment
        from src.utils.config import Config
        from src.utils.logger import setup_logger
        from datetime import datetime

        # Setup
        config = Config(config_dict)

        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)

        # Generate log filename with timestamp and algorithm name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"quick_test_{algorithm}_{timestamp}.log"

        # Setup logger with file output
        logger = setup_logger(
            log_level=config.experiment.log_level,
            log_file=log_filename,
            log_dir="logs",
            logger_name=f"quick_test_{algorithm}"
        )
        
        # Run experiment
        print(f"Starting quick test with {algorithm}...")
        final_metrics = run_experiment(config, algorithm)
        
        # Print results
        print(f"\n{algorithm.upper()} Quick Test Results:")
        print("-" * 40)
        for metric, value in final_metrics.items():
            if isinstance(value, (int, float)):
                print(f"{metric:15}: {value:.4f}")
            else:
                print(f"{metric:15}: {value}")
        
        return final_metrics
        
    except Exception as e:
        print(f"Error in {algorithm} quick test: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    parser = argparse.ArgumentParser(description="Quick Test D³AFD Algorithms")
    parser.add_argument("--algorithm", type=str,
                       choices=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       help="Algorithm to test")
    parser.add_argument("--all", action="store_true",
                       help="Test all algorithms")
    parser.add_argument("--device", type=str, default="auto",
                       choices=["auto", "cuda", "cpu"],
                       help="Device to use")

    args = parser.parse_args()

    # Setup main logger for overall test results
    from datetime import datetime
    os.makedirs('logs', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if args.all:
        main_log_filename = f"quick_test_all_algorithms_{timestamp}.log"
    else:
        main_log_filename = f"quick_test_{args.algorithm}_{timestamp}.log"

    # Setup basic logging for main function
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join('logs', main_log_filename), encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    main_logger = logging.getLogger("quick_test_main")

    # Determine device
    if args.device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        device = args.device

    print(f"Using device: {device}")
    main_logger.info(f"Quick test started with device: {device}")
    main_logger.info(f"Main log file: logs/{main_log_filename}")
    
    if args.all:
        # Test all algorithms
        algorithms = ["dkd", "dkdm", "diffdfkd", "diffkd"]
        results = {}

        main_logger.info("Starting tests for all algorithms")

        for algorithm in algorithms:
            main_logger.info(f"Testing algorithm: {algorithm}")
            result = run_quick_test(algorithm)
            results[algorithm] = result

            if result:
                accuracy = result.get('accuracy', 'N/A')
                f1_macro = result.get('f1_macro', 'N/A')
                main_logger.info(f"{algorithm.upper()} - SUCCESS: Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}")
            else:
                main_logger.error(f"{algorithm.upper()} - FAILED")

        # Summary
        print(f"\n{'='*60}")
        print("QUICK TEST SUMMARY")
        print(f"{'='*60}")
        main_logger.info("QUICK TEST SUMMARY")

        for algorithm, metrics in results.items():
            if metrics:
                accuracy = metrics.get('accuracy', 'N/A')
                f1_macro = metrics.get('f1_macro', 'N/A')
                summary_line = f"{algorithm.upper():10} - Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}"
                print(summary_line)
                main_logger.info(summary_line)
            else:
                failure_line = f"{algorithm.upper():10} - FAILED"
                print(failure_line)
                main_logger.error(failure_line)
    
    elif args.algorithm:
        # Test single algorithm
        main_logger.info(f"Testing single algorithm: {args.algorithm}")
        result = run_quick_test(args.algorithm)

        if result:
            accuracy = result.get('accuracy', 'N/A')
            f1_macro = result.get('f1_macro', 'N/A')
            main_logger.info(f"{args.algorithm.upper()} - SUCCESS: Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}")
        else:
            main_logger.error(f"{args.algorithm.upper()} - FAILED")
        if result:
            print(f"\n{args.algorithm.upper()} quick test completed successfully!")
        else:
            print(f"\n{args.algorithm.upper()} quick test failed!")
    
    else:
        print("Please specify --algorithm or use --all to test all algorithms")
        print("Example: python quick_test_algorithms.py --algorithm dkdm")
        print("Example: python quick_test_algorithms.py --all")
        main_logger.info("No algorithm specified, showing help")

    main_logger.info("Quick test session completed")

if __name__ == "__main__":
    main()


