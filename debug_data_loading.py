#!/usr/bin/env python3
"""
Debug script to test data loading issues
"""

import os
import sys
import yaml
import torch

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data.amazon_dataset import AmazonReviewDataset
from src.utils.config import Config

def setup_quick_test_config():
    """Create a minimal configuration for quick testing"""
    config = {
        'experiment': {
            'name': 'd3afd_quick_test_debug',
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'seed': 42,
            'output_dir': 'outputs/debug_test',
            'data_dir': 'data/debug_test',
            'log_level': 'INFO'
        },
        'data': {
            'dataset_name': 'amazon_reviews',
            'data_dir': 'data/amazon_reviews',
            'domains': ['Electronics', 'Books'],
            'num_clients': 2,
            'samples_per_client': 100,
            'max_length': 256,
            'max_seq_length': 256,
            'train_split': 0.8,
            'val_split': 0.1,
            'test_split': 0.1,
            'num_workers': 2
        },
        'model': {
            'model_name': 'distilbert-base-uncased',
            'student_model': 'distilbert-base-uncased',
            'teacher_model': 'distilbert-base-uncased',
            'teacher_hidden_size': 768,
            'generator_model': 't5-small',
            'generator_max_length': 128,
            'discriminator_hidden_size': 256,
            'num_labels': 3,
            'num_classes': 3,
            'hidden_size': 768,
            'dropout': 0.1,
            'dropout_rate': 0.1,
            'freeze_backbone': False
        }
    }
    return config

def test_data_loading():
    """Test data loading step by step"""
    print("=== Data Loading Debug Test ===")
    
    # Create config
    config_dict = setup_quick_test_config()
    config = Config(config_dict)
    
    print(f"Config created successfully")
    print(f"Config.data.domains: {config.data.domains}")
    print(f"Config.model.model_name: {config.model.model_name}")
    
    # Test dataset initialization
    print("\n1. Testing dataset initialization with config...")
    try:
        dataset = AmazonReviewDataset(config=config)
        print(f"Dataset initialized with {len(dataset.texts)} samples")
        print(f"Available domains in dataset: {list(set(dataset.domains))}")
        
        if len(dataset.texts) > 0:
            print(f"Sample text: {dataset.texts[0]}")
            print(f"Sample label: {dataset.labels[0]}")
            print(f"Sample domain: {dataset.domains[0]}")
        
        # Test domain data retrieval
        print("\n2. Testing domain data retrieval...")
        for domain in config.data.domains:
            domain_data = dataset.get_domain_data(domain)
            print(f"Domain '{domain}': {len(domain_data)} samples")
            
        # Test test data retrieval
        print("\n3. Testing test data retrieval...")
        test_data = dataset.get_test_data()
        print(f"Test data: {len(test_data)} samples")
        
        return True
        
    except Exception as e:
        print(f"Error during dataset initialization: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_manual_data_creation():
    """Test manual data creation"""
    print("\n=== Manual Data Creation Test ===")
    
    # Create sample data manually
    texts = [
        "This phone is amazing! Great battery life.",
        "The laptop is okay, but could be faster.",
        "Terrible headphones, very poor sound quality.",
        "Excellent book! Couldn't put it down.",
        "The story was decent but ending disappointing.",
        "Boring book, waste of time and money."
    ]
    
    labels = [2, 1, 0, 2, 1, 0]  # positive, neutral, negative
    domains = ['Electronics', 'Electronics', 'Electronics', 'Books', 'Books', 'Books']
    
    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained('distilbert-base-uncased')
        
        dataset = AmazonReviewDataset(
            texts=texts,
            labels=labels,
            domains=domains,
            tokenizer=tokenizer,
            max_length=256
        )
        
        print(f"Manual dataset created with {len(dataset.texts)} samples")
        
        # Test domain data retrieval
        for domain in ['Electronics', 'Books']:
            domain_data = dataset.get_domain_data(domain)
            print(f"Domain '{domain}': {len(domain_data)} samples")
            
        return True
        
    except Exception as e:
        print(f"Error during manual dataset creation: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("Starting data loading debug...")
    
    # Test 1: Config-based loading
    success1 = test_data_loading()
    
    # Test 2: Manual data creation
    success2 = test_manual_data_creation()
    
    print(f"\n=== Results ===")
    print(f"Config-based loading: {'SUCCESS' if success1 else 'FAILED'}")
    print(f"Manual data creation: {'SUCCESS' if success2 else 'FAILED'}")
