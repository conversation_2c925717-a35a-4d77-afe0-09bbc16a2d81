#!/usr/bin/env python3
"""
GPU Accelerated Test Script for D³AFD Multi-Algorithm Framework
Optimized for maximum GPU utilization and training speed
"""

import os
import sys
import argparse
import logging
import yaml
import torch
import gc
import time
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def setup_gpu_environment():
    """Setup optimized GPU environment"""
    print("🚀 Setting up GPU acceleration environment...")
    
    # Enable CUDA optimizations
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    
    # Set environment variables for maximum performance
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:1024'
    os.environ['TOKENIZERS_PARALLELISM'] = 'true'
    os.environ['OMP_NUM_THREADS'] = '12'
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'
    
    # Enable all available optimizations
    try:
        torch.backends.cuda.enable_flash_sdp(True)
        torch.backends.cuda.enable_mem_efficient_sdp(True)
        torch.backends.cuda.enable_math_sdp(True)
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
    except:
        pass
    
    # Check and configure GPU memory
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        gpu_props = torch.cuda.get_device_properties(current_device)
        gpu_memory = gpu_props.total_memory / 1024**3
        
        print(f"📊 GPU Information:")
        print(f"   - Device: {gpu_props.name}")
        print(f"   - Total Memory: {gpu_memory:.2f} GB")
        print(f"   - Available GPUs: {gpu_count}")
        
        # Clear any existing memory
        torch.cuda.empty_cache()
        gc.collect()
        
        # Set aggressive memory usage
        if gpu_memory > 16:
            memory_fraction = 0.95  # Use 95% for high-end GPUs
        elif gpu_memory > 12:
            memory_fraction = 0.90  # Use 90% for mid-range GPUs
        else:
            memory_fraction = 0.85  # Use 85% for lower-end GPUs
        
        torch.cuda.set_per_process_memory_fraction(memory_fraction)
        print(f"   - Memory Allocation: {memory_fraction*100:.0f}%")
        
        return gpu_memory, memory_fraction
    else:
        print("❌ CUDA not available!")
        return 0, 0

def create_gpu_optimized_config(algorithm, gpu_memory):
    """Create GPU-optimized configuration"""
    
    # Load base configuration
    base_config_path = 'configs/d3afd_multi_algorithm.yaml'
    with open(base_config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # GPU-optimized settings based on available memory
    if gpu_memory > 16:
        # High-end GPU settings
        config['training']['local_batch_size'] = 64
        config['training']['distillation_batch_size'] = 128
        config['training']['pseudo_samples_per_domain'] = 2000
        config['data']['num_workers'] = 16
        config['data']['samples_per_client'] = 500
    elif gpu_memory > 12:
        # Mid-range GPU settings
        config['training']['local_batch_size'] = 48
        config['training']['distillation_batch_size'] = 96
        config['training']['pseudo_samples_per_domain'] = 1500
        config['data']['num_workers'] = 12
        config['data']['samples_per_client'] = 400
    elif gpu_memory > 8:
        # Standard GPU settings
        config['training']['local_batch_size'] = 32
        config['training']['distillation_batch_size'] = 64
        config['training']['pseudo_samples_per_domain'] = 1000
        config['data']['num_workers'] = 8
        config['data']['samples_per_client'] = 300
    else:
        # Conservative settings for smaller GPUs
        config['training']['local_batch_size'] = 24
        config['training']['distillation_batch_size'] = 48
        config['training']['pseudo_samples_per_domain'] = 800
        config['data']['num_workers'] = 6
        config['data']['samples_per_client'] = 200
    
    # Common optimizations
    config['training']['batch_size'] = config['training']['local_batch_size']
    config['training']['cache_refresh_interval'] = 1  # More aggressive caching
    config['training']['num_rounds'] = 5  # More rounds for better results
    config['training']['local_epochs'] = 3
    config['training']['distillation_epochs'] = 3
    
    # Memory management
    config['memory']['enable_memory_management'] = True
    config['memory']['max_memory_usage'] = 0.90
    config['memory']['cleanup_frequency'] = 1
    config['memory']['force_cleanup_epochs'] = True
    
    # Text generation optimizations
    config['text_generation']['max_length'] = 128
    config['text_generation']['temperature'] = 0.9
    config['text_generation']['do_sample'] = True
    
    # Experiment settings
    config['experiment']['name'] = f'd3afd_gpu_accelerated_{algorithm}'
    config['experiment']['output_dir'] = f'outputs/gpu_accelerated_{algorithm}'
    
    # Disable unnecessary logging for speed
    config['logging']['tensorboard'] = False
    config['logging']['wandb']['enabled'] = False
    config['logging']['log_frequency'] = 20
    
    return config

def run_gpu_accelerated_test(algorithm):
    """Run GPU-accelerated test"""
    print(f"\n{'='*70}")
    print(f"🚀 GPU ACCELERATED TEST: {algorithm.upper()}")
    print(f"{'='*70}")
    
    try:
        # Setup GPU environment
        gpu_memory, memory_fraction = setup_gpu_environment()
        
        if gpu_memory == 0:
            print("❌ Cannot run GPU accelerated test without CUDA")
            return None
        
        # Create optimized config
        config_dict = create_gpu_optimized_config(algorithm, gpu_memory)
        
        # Import required modules
        from train_d3afd_multi_algorithm import run_experiment
        from src.utils.config import Config
        from src.utils.logger import setup_logger
        
        # Setup config and logger
        config = Config(config_dict)
        
        os.makedirs('logs', exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"gpu_accelerated_{algorithm}_{timestamp}.log"
        
        logger = setup_logger(
            log_level="INFO",
            log_file=log_filename,
            log_dir="logs",
            logger_name=f"gpu_accelerated_{algorithm}"
        )
        
        # Log configuration
        logger.info("🚀 GPU Accelerated Training Configuration:")
        logger.info(f"   - Algorithm: {algorithm}")
        logger.info(f"   - GPU Memory: {gpu_memory:.2f} GB ({memory_fraction*100:.0f}% allocated)")
        logger.info(f"   - Local Batch Size: {config.training.local_batch_size}")
        logger.info(f"   - Distillation Batch Size: {config.training.distillation_batch_size}")
        logger.info(f"   - Data Workers: {config.data.num_workers}")
        logger.info(f"   - Samples per Client: {config.data.samples_per_client}")
        logger.info(f"   - Pseudo Samples per Domain: {config.training.pseudo_samples_per_domain}")
        
        # Run experiment with timing
        print(f"🏃 Starting GPU-accelerated training for {algorithm}...")
        start_time = time.time()
        
        final_metrics = run_experiment(config, algorithm)
        
        end_time = time.time()
        training_time = (end_time - start_time) / 60
        
        # Print results
        print(f"\n🎯 {algorithm.upper()} GPU Accelerated Results:")
        print("=" * 50)
        print(f"⏱️  Training Time: {training_time:.2f} minutes")
        print(f"🔧 GPU Memory Used: {gpu_memory:.2f} GB ({memory_fraction*100:.0f}%)")
        print(f"📊 Batch Sizes: Local={config.training.local_batch_size}, Distillation={config.training.distillation_batch_size}")
        print("-" * 50)
        
        for metric, value in final_metrics.items():
            if isinstance(value, (int, float)):
                print(f"📈 {metric:15}: {value:.4f}")
            else:
                print(f"📈 {metric:15}: {value}")
        
        # Log final results
        logger.info(f"🎯 Training completed in {training_time:.2f} minutes")
        logger.info(f"📊 Final metrics: {final_metrics}")
        
        return final_metrics
        
    except Exception as e:
        print(f"❌ Error in GPU accelerated test: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    parser = argparse.ArgumentParser(description="GPU Accelerated D³AFD Algorithm Test")
    parser.add_argument("--algorithm", type=str,
                       choices=["dkd", "dkdm", "diffdfkd", "diffkd"],
                       help="Algorithm to test")
    parser.add_argument("--all", action="store_true",
                       help="Test all algorithms")
    
    args = parser.parse_args()
    
    if args.all:
        algorithms = ["dkd", "dkdm", "diffdfkd", "diffkd"]
        results = {}
        
        print("🚀 Running GPU accelerated tests for all algorithms...")
        
        for algorithm in algorithms:
            result = run_gpu_accelerated_test(algorithm)
            results[algorithm] = result
            
            # Clear GPU memory between tests
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
                time.sleep(2)  # Brief pause between tests
        
        # Summary
        print(f"\n{'='*70}")
        print("🏆 GPU ACCELERATED TEST SUMMARY")
        print(f"{'='*70}")
        
        for algorithm, metrics in results.items():
            if metrics:
                accuracy = metrics.get('accuracy', 'N/A')
                f1_macro = metrics.get('f1_macro', 'N/A')
                print(f"✅ {algorithm.upper():10} - Accuracy: {accuracy:.4f}, F1-Macro: {f1_macro:.4f}")
            else:
                print(f"❌ {algorithm.upper():10} - FAILED")
    
    elif args.algorithm:
        result = run_gpu_accelerated_test(args.algorithm)
        if result:
            print(f"\n✅ {args.algorithm.upper()} GPU accelerated test completed successfully!")
        else:
            print(f"\n❌ {args.algorithm.upper()} GPU accelerated test failed!")
    
    else:
        print("Please specify --algorithm or use --all")
        print("Example: python run_gpu_accelerated_test.py --algorithm dkdm")
        print("Example: python run_gpu_accelerated_test.py --all")

if __name__ == "__main__":
    main()
